import { EventEmitter } from 'events';
import { PublicKey } from '@solana/web3.js';
import cron from 'node-cron';
import {
  TokenDetectedEvent,
  UserWallet,
  Position,
  User
} from '../types';
import RaydiumPoolMonitor from '../listeners/raydium';
import TokenFilterEngine from '../filters/tokenChecks';
import BuyExecutor from '../actions/buy';
import SellExecutor from '../actions/sell';
import SupabaseManager from '../database/supabase';
import { getUserWallet, getTokenBalance } from '../utils/wallet';
import { notificationManager } from '../utils/notifications';
import { logger, logError } from '../utils/logger';
import config from '../config';

export class AutoSniperEngine extends EventEmitter {
  private poolMonitor: RaydiumPoolMonitor;
  private tokenFilter: TokenFilterEngine;
  private buyExecutor: BuyExecutor;
  private sellExecutor: SellExecutor;
  private database: SupabaseManager;
  private activePositions = new Map<string, Position>();
  private priceMonitoringTokens = new Set<string>();
  private isRunning = false;
  private sniperUsers: User[] = [];

  constructor() {
    super();
    this.poolMonitor = new RaydiumPoolMonitor();
    this.tokenFilter = new TokenFilterEngine();
    this.buyExecutor = new BuyExecutor();
    this.sellExecutor = new SellExecutor();
    this.database = new SupabaseManager();
    
    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Listen for new token detections from pool monitor
    this.poolMonitor.on('tokenDetected', this.handleNewTokenDetected.bind(this));
    
    // Setup continuous price monitoring for auto-sell
    this.setupContinuousPriceMonitoring();
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Auto-sniper already running');
      return;
    }

    try {
      this.isRunning = true;
      
      logger.info('🎯 STARTING AUTOMATED SNIPER ENGINE');
      
      // Load active sniper users
      await this.loadSniperUsers();
      
      // Start pool monitoring for new token detection
      await this.poolMonitor.startMonitoring();
      
      // Load existing positions for price monitoring
      await this.loadActivePositions();

      logger.info('🚀 AUTOMATED SNIPER ENGINE STARTED', {
        activeUsers: this.sniperUsers.length,
        monitoringPositions: this.activePositions.size,
      });
      
      // Send startup notification
      await notificationManager.sendNotification({
        type: 'alert',
        title: '🎯 Auto-Sniper Started',
        message: `Monitoring ${this.sniperUsers.length} users for new token opportunities`,
        data: {
          'Active Users': this.sniperUsers.length,
          'Min Liquidity': `${config.trading.minLiquiditySol} SOL`,
          'Buy Amount': `${config.trading.defaultBuyAmountSol} SOL`,
          'Take Profit': `${config.profitLoss.takeProfitPercent}%`,
          'Stop Loss': `${config.profitLoss.stopLossPercent}%`,
        },
        timestamp: new Date(),
      });

    } catch (error) {
      this.isRunning = false;
      logError(error as Error, { context: 'autoSniperStart' });
      throw error;
    }
  }

  async stop(): Promise<void> {
    this.isRunning = false;
    await this.poolMonitor.stopMonitoring();
    
    logger.info('🛑 AUTOMATED SNIPER ENGINE STOPPED');
    
    await notificationManager.sendNotification({
      type: 'alert',
      title: '🛑 Auto-Sniper Stopped',
      message: 'Automated sniper engine has been stopped',
      timestamp: new Date(),
    });
  }

  /**
   * MAIN AUTOMATED FLOW: Handle newly detected tokens
   */
  private async handleNewTokenDetected(event: TokenDetectedEvent): Promise<void> {
    try {
      logger.info('🔍 NEW TOKEN DETECTED - STARTING AUTOMATED ANALYSIS', {
        tokenAddress: event.tokenAddress,
        poolId: event.poolId,
        liquidity: event.liquidity,
      });

      // Emit token detected event for real-time frontend updates
      this.emit('tokenDetected', event);

      // Step 1: Run comprehensive token filters
      const filterResult = await this.runTokenFilters(event);
      
      if (!filterResult.passed) {
        logger.info('❌ TOKEN REJECTED BY FILTERS', {
          tokenAddress: event.tokenAddress,
          reason: filterResult.reason,
          score: filterResult.score,
        });

        await notificationManager.notifyTokenDetected({
          tokenAddress: event.tokenAddress,
          poolId: event.poolId,
          liquidity: event.liquidity,
          action: 'skipped',
          reason: filterResult.reason,
        });

        return;
      }

      logger.info('✅ TOKEN PASSED ALL FILTERS - EXECUTING AUTOMATED BUYS', {
        tokenAddress: event.tokenAddress,
        score: filterResult.score,
        liquidity: event.liquidity,
      });

      // Step 2: Execute automatic buys for all active users
      await this.executeAutomaticBuys(event.tokenAddress);

      // Step 3: Start price monitoring for auto-sell
      this.startPriceMonitoring(event.tokenAddress);

    } catch (error) {
      logError(error as Error, { context: 'handleNewTokenDetected', event });
    }
  }

  /**
   * Execute automatic buys for all users with sniper enabled
   */
  private async executeAutomaticBuys(tokenAddress: string): Promise<void> {
    try {
      logger.info('🚀 EXECUTING AUTOMATIC BUYS FOR ALL SNIPER USERS', { 
        tokenAddress,
        userCount: this.sniperUsers.length 
      });

      const buyPromises = this.sniperUsers.map(async (user) => {
        try {
          const wallets = await this.database.getUserWallets(user.id);
          
          for (const walletData of wallets) {
            try {
              const userWallet = await getUserWallet(walletData);
              
              // Check sufficient balance
              const requiredAmount = user.settings.trading.buyAmount + 0.01; // Buy amount + fees
              if (userWallet.balance.sol < requiredAmount) {
                logger.warn('⚠️ INSUFFICIENT BALANCE FOR AUTO-BUY', {
                  userId: user.id,
                  walletAddress: userWallet.publicKey.toString(),
                  balance: userWallet.balance.sol,
                  required: requiredAmount,
                });
                continue;
              }

              logger.info('💰 EXECUTING AUTO-BUY VIA JUPITER', {
                userId: user.id,
                walletAddress: userWallet.publicKey.toString(),
                tokenAddress,
                amount: user.settings.trading.buyAmount,
              });

              // Execute buy using Jupiter swap
              const buyResult = await this.buyExecutor.quickBuy(
                userWallet, 
                tokenAddress, 
                user.settings.trading.buyAmount
              );
              
              if (buyResult.success) {
                logger.info('✅ AUTO-BUY SUCCESSFUL VIA JUPITER', {
                  userId: user.id,
                  tokenAddress,
                  signature: buyResult.signature,
                  amount: buyResult.amount,
                  price: buyResult.price,
                  slippage: buyResult.slippage,
                });

                // Save successful transaction
                await this.database.saveTransaction({
                  userId: user.id,
                  walletAddress: userWallet.publicKey.toString(),
                  type: 'buy',
                  tokenAddress,
                  amount: buyResult.amount || 0,
                  price: buyResult.price || 0,
                  slippage: buyResult.slippage || 0,
                  signature: buyResult.signature || '',
                  status: 'confirmed',
                });

                // Create position for auto-sell monitoring
                await this.createPositionForAutoSell(user.id, userWallet, tokenAddress, buyResult);
                
                // Setup automatic sell alerts
                await this.setupAutoSellAlerts(user.id, tokenAddress, buyResult.price || 0, user.settings);

              } else {
                logger.error('❌ AUTO-BUY FAILED', {
                  userId: user.id,
                  tokenAddress,
                  error: buyResult.error,
                });

                // Save failed transaction
                await this.database.saveTransaction({
                  userId: user.id,
                  walletAddress: userWallet.publicKey.toString(),
                  type: 'buy',
                  tokenAddress,
                  amount: 0,
                  price: 0,
                  slippage: 0,
                  signature: '',
                  status: 'failed',
                  error: buyResult.error || 'Unknown error',
                });
              }

            } catch (walletError) {
              logError(walletError as Error, { 
                context: 'executeAutoBuyForWallet', 
                userId: user.id, 
                walletId: walletData.id 
              });
            }
          }
        } catch (userError) {
          logError(userError as Error, { context: 'executeAutoBuyForUser', userId: user.id });
        }
      });

      // Execute all buys with concurrency limit
      const chunks = this.chunkArray(buyPromises, config.limits.maxConcurrentTransactions);
      
      for (const chunk of chunks) {
        await Promise.allSettled(chunk);
        // Small delay between chunks
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      logger.info('🎯 AUTOMATIC BUY EXECUTION COMPLETED', {
        tokenAddress,
        totalUsers: this.sniperUsers.length,
      });
      
    } catch (error) {
      logError(error as Error, { context: 'executeAutomaticBuys', tokenAddress });
    }
  }

  /**
   * Setup continuous price monitoring for automatic selling
   */
  private setupContinuousPriceMonitoring(): void {
    // Monitor prices every 2 seconds for auto-sell triggers
    cron.schedule('*/2 * * * * *', async () => {
      if (!this.isRunning) return;
      
      try {
        await this.monitorPricesForAutoSell();
      } catch (error) {
        logError(error as Error, { context: 'continuousPriceMonitoring' });
      }
    });

    logger.info('📊 CONTINUOUS PRICE MONITORING SETUP - CHECKING EVERY 2 SECONDS');
  }

  /**
   * Monitor all active positions for auto-sell triggers
   */
  private async monitorPricesForAutoSell(): Promise<void> {
    if (this.priceMonitoringTokens.size === 0) return;

    const monitoringPromises = Array.from(this.priceMonitoringTokens).map(async (tokenAddress) => {
      try {
        // Get current price via Jupiter
        const currentPrice = await this.getCurrentTokenPriceViaJupiter(tokenAddress);
        if (!currentPrice) return;

        // Check all positions for this token
        for (const [, position] of this.activePositions.entries()) {
          if (position.tokenAddress === tokenAddress) {
            await this.checkPositionForAutoSell(position, currentPrice);
          }
        }
      } catch (error) {
        logError(error as Error, { context: 'monitorTokenPrice', tokenAddress });
      }
    });

    await Promise.allSettled(monitoringPromises);
  }

  /**
   * Get current token price via Jupiter API
   */
  private async getCurrentTokenPriceViaJupiter(tokenAddress: string): Promise<number | null> {
    try {
      // Method 1: Try Jupiter price API
      const priceResponse = await fetch(
        `${config.jupiter.apiUrl}/price?ids=${tokenAddress}&vsToken=So11111111111111111111111111111111111111112`
      );
      
      if (priceResponse.ok) {
        const priceData = await priceResponse.json();
        const price = priceData.data?.[tokenAddress]?.price;
        if (price) return parseFloat(price);
      }

      // Method 2: Fallback to quote API for price discovery
      const quoteResponse = await fetch(`${config.jupiter.apiUrl}/quote?` + new URLSearchParams({
        inputMint: tokenAddress,
        outputMint: 'So11111111111111111111111111111111111111112', // SOL
        amount: '1000000', // 1 token (6 decimals)
        slippageBps: '100',
      }));

      if (quoteResponse.ok) {
        const quote = await quoteResponse.json();
        const inputAmount = parseFloat(quote.inAmount);
        const outputAmount = parseFloat(quote.outAmount);

        if (inputAmount > 0 && outputAmount > 0) {
          return outputAmount / inputAmount; // Price in SOL per token
        }
      }

      return null;
    } catch (error) {
      logError(error as Error, { context: 'getCurrentTokenPriceViaJupiter', tokenAddress });
      return null;
    }
  }

  /**
   * Check if position should trigger auto-sell
   */
  private async checkPositionForAutoSell(position: Position, currentPrice: number): Promise<void> {
    try {
      const buyPrice = position.averageBuyPrice;
      const priceChangePercent = ((currentPrice - buyPrice) / buyPrice) * 100;

      // Update position current price
      position.currentPrice = currentPrice;
      position.pnl = (currentPrice - buyPrice) * position.amount;
      position.pnlPercent = priceChangePercent;

      // Get user settings
      const user = await this.database.getUser(position.userId);
      if (!user) return;

      const { takeProfitPercent, stopLossPercent } = user.settings.trading;

      // Check take profit trigger
      if (priceChangePercent >= takeProfitPercent) {
        logger.info('📈 TAKE PROFIT TRIGGERED - EXECUTING AUTO-SELL', {
          userId: position.userId,
          tokenAddress: position.tokenAddress,
          buyPrice,
          currentPrice,
          profitPercent: priceChangePercent,
          targetPercent: takeProfitPercent,
        });

        await this.executeAutoSellForPosition(position, 'take_profit', currentPrice);
        return;
      }

      // Check stop loss trigger
      if (priceChangePercent <= -stopLossPercent) {
        logger.info('📉 STOP LOSS TRIGGERED - EXECUTING AUTO-SELL', {
          userId: position.userId,
          tokenAddress: position.tokenAddress,
          buyPrice,
          currentPrice,
          lossPercent: priceChangePercent,
          targetPercent: -stopLossPercent,
        });

        await this.executeAutoSellForPosition(position, 'stop_loss', currentPrice);
        return;
      }

      // Log price monitoring (debug level)
      logger.debug('📊 Price monitored', {
        tokenAddress: position.tokenAddress,
        buyPrice,
        currentPrice,
        changePercent: priceChangePercent,
      });

    } catch (error) {
      logError(error as Error, { context: 'checkPositionForAutoSell', position });
    }
  }

  /**
   * Execute automatic sell for a position
   */
  private async executeAutoSellForPosition(
    position: Position, 
    triggerType: 'take_profit' | 'stop_loss',
    currentPrice: number
  ): Promise<void> {
    try {
      // Get user wallet
      const walletData = await this.database.getWalletByPublicKey(position.walletAddress);
      if (!walletData) {
        logger.error('Wallet not found for auto-sell', { position });
        return;
      }

      const userWallet = await getUserWallet(walletData);

      // Check current token balance
      const tokenBalance = await getTokenBalance(
        userWallet.publicKey, 
        new PublicKey(position.tokenAddress)
      );

      if (tokenBalance <= 0) {
        logger.warn('No token balance for auto-sell', {
          userId: position.userId,
          tokenAddress: position.tokenAddress,
        });
        
        // Remove position since no tokens left
        this.activePositions.delete(`${position.userId}-${position.tokenAddress}`);
        this.priceMonitoringTokens.delete(position.tokenAddress);
        return;
      }

      logger.info('🔴 EXECUTING AUTO-SELL VIA JUPITER', {
        userId: position.userId,
        tokenAddress: position.tokenAddress,
        triggerType,
        balance: tokenBalance,
        currentPrice,
        buyPrice: position.averageBuyPrice,
      });

      // Execute sell via Jupiter
      const sellResult = await this.sellExecutor.quickSell(
        userWallet,
        position.tokenAddress,
        100 // Sell 100% of holdings
      );

      if (sellResult.success) {
        const pnlPercent = ((currentPrice - position.averageBuyPrice) / position.averageBuyPrice) * 100;

        logger.info('✅ AUTO-SELL SUCCESSFUL VIA JUPITER', {
          userId: position.userId,
          tokenAddress: position.tokenAddress,
          signature: sellResult.signature,
          amount: sellResult.amount,
          price: sellResult.price,
          pnl: pnlPercent,
          triggerType,
        });

        // Save sell transaction
        await this.database.saveTransaction({
          userId: position.userId,
          walletAddress: userWallet.publicKey.toString(),
          type: 'sell',
          tokenAddress: position.tokenAddress,
          amount: sellResult.amount || 0,
          price: sellResult.price || 0,
          slippage: sellResult.slippage || 0,
          signature: sellResult.signature || '',
          status: 'confirmed',
        });

        // Close position
        position.status = 'closed';
        position.closedAt = new Date();
        position.currentPrice = currentPrice;
        position.pnl = (currentPrice - position.averageBuyPrice) * position.amount;
        position.pnlPercent = pnlPercent;

        // Remove from active monitoring
        this.activePositions.delete(`${position.userId}-${position.tokenAddress}`);
        
        // Check if any other users have this token, if not stop monitoring
        const hasOtherPositions = Array.from(this.activePositions.values())
          .some(p => p.tokenAddress === position.tokenAddress);
        
        if (!hasOtherPositions) {
          this.priceMonitoringTokens.delete(position.tokenAddress);
        }

        // Send success notification
        await notificationManager.notifySell({
          tokenAddress: position.tokenAddress,
          amount: sellResult.amount || 0,
          price: sellResult.price || 0,
          pnl: pnlPercent,
          signature: sellResult.signature || '',
        });

      } else {
        logger.error('❌ AUTO-SELL FAILED', {
          userId: position.userId,
          tokenAddress: position.tokenAddress,
          error: sellResult.error,
          triggerType,
        });

        // Save failed transaction but keep position active for retry
        await this.database.saveTransaction({
          userId: position.userId,
          walletAddress: userWallet.publicKey.toString(),
          type: 'sell',
          tokenAddress: position.tokenAddress,
          amount: 0,
          price: 0,
          slippage: 0,
          signature: '',
          status: 'failed',
          error: sellResult.error || 'Unknown error',
        });
      }

    } catch (error) {
      logError(error as Error, { context: 'executeAutoSellForPosition', position, triggerType });
    }
  }

  private async runTokenFilters(event: TokenDetectedEvent): Promise<any> {
    // Create mock liquidity pool for filtering
    const liquidityPool = {
      poolId: event.poolId,
      baseMint: event.tokenAddress,
      quoteMint: 'So11111111111111111111111111111111111111112',
      liquidity: { sol: event.liquidity, usd: event.liquidity * 100 },
    };

    return await this.tokenFilter.filterToken(event.tokenAddress, liquidityPool as any, event.metadata);
  }

  private async createPositionForAutoSell(
    userId: string,
    wallet: UserWallet,
    tokenAddress: string,
    buyResult: any
  ): Promise<void> {
    const position: Omit<Position, 'id' | 'createdAt'> = {
      userId,
      walletAddress: wallet.publicKey.toString(),
      tokenAddress,
      amount: buyResult.amount,
      averageBuyPrice: buyResult.price,
      currentPrice: buyResult.price,
      pnl: 0,
      pnlPercent: 0,
      status: 'active',
    };

    const savedPosition = await this.database.savePosition(position);
    this.activePositions.set(`${userId}-${tokenAddress}`, savedPosition);
  }

  private async setupAutoSellAlerts(
    userId: string,
    tokenAddress: string,
    buyPrice: number,
    userSettings: any
  ): Promise<void> {
    // No need for separate alerts - we monitor positions directly
    logger.info('🎯 AUTO-SELL MONITORING SETUP', {
      userId,
      tokenAddress,
      buyPrice,
      takeProfitTarget: userSettings.trading.takeProfitPercent,
      stopLossTarget: userSettings.trading.stopLossPercent,
    });
  }

  private startPriceMonitoring(tokenAddress: string): void {
    this.priceMonitoringTokens.add(tokenAddress);
    logger.info('📊 STARTED PRICE MONITORING FOR AUTO-SELL', { tokenAddress });
  }

  private async loadSniperUsers(): Promise<void> {
    try {
      // Load all users with sniper enabled
      // For now, create a mock user for testing
      this.sniperUsers = []; // Would load from database
      logger.info('Sniper users loaded', { count: this.sniperUsers.length });
    } catch (error) {
      logError(error as Error, { context: 'loadSniperUsers' });
    }
  }

  private async loadActivePositions(): Promise<void> {
    try {
      // Load all active positions from database
      logger.info('Active positions loaded', { count: this.activePositions.size });
    } catch (error) {
      logError(error as Error, { context: 'loadActivePositions' });
    }
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  getStatus(): any {
    return {
      isRunning: this.isRunning,
      activeUsers: this.sniperUsers.length,
      activePositions: this.activePositions.size,
      monitoringTokens: this.priceMonitoringTokens.size,
      poolsDetected: this.poolMonitor?.getPoolsDetected() || 0,
      recentTokens: this.poolMonitor?.getRecentTokens() || 0,
      config: {
        minLiquidity: config.trading.minLiquiditySol,
        buyAmount: config.trading.defaultBuyAmountSol,
        takeProfitPercent: config.profitLoss.takeProfitPercent,
        stopLossPercent: config.profitLoss.stopLossPercent,
        autoSellEnabled: config.profitLoss.autoSellEnabled,
      },
    };
  }

  // Additional methods for API integration
  async getActivePositions(): Promise<Position[]> {
    return Array.from(this.activePositions.values());
  }

  async getRecentTrades(limit: number = 50): Promise<any[]> {
    try {
      // Get recent trades from database
      return await this.database.getRecentTrades(limit);
    } catch (error) {
      logError(error as Error, { context: 'getRecentTrades' });
      return [];
    }
  }

  async getPriceAlerts(): Promise<any[]> {
    try {
      // Get active price alerts from database
      return await this.database.getActivePriceAlerts();
    } catch (error) {
      logError(error as Error, { context: 'getPriceAlerts' });
      return [];
    }
  }

  async getDetectedTokens(limit: number = 20): Promise<any[]> {
    try {
      // Get recently detected tokens from database
      return await this.database.getRecentlyDetectedTokens(limit);
    } catch (error) {
      logError(error as Error, { context: 'getDetectedTokens' });
      return [];
    }
  }

  async executeBuy(params: any): Promise<any> {
    try {
      const userWallet = await getUserWallet(params.walletAddress);
      const result = await this.buyExecutor.quickBuy(
        userWallet,
        params.tokenAddress,
        params.amount,
        params.slippage
      );

      // Emit buy executed event
      this.emit('buyExecuted', {
        ...params,
        result,
        timestamp: new Date(),
      });

      return result;
    } catch (error) {
      logError(error as Error, { context: 'executeBuy', params });
      throw error;
    }
  }

  async executeSell(params: any): Promise<any> {
    try {
      const userWallet = await getUserWallet(params.walletAddress);
      const result = await this.sellExecutor.quickSell(
        userWallet,
        params.tokenAddress,
        params.percentage || 100
      );

      // Emit sell executed event
      this.emit('sellExecuted', {
        ...params,
        result,
        timestamp: new Date(),
      });

      return result;
    } catch (error) {
      logError(error as Error, { context: 'executeSell', params });
      throw error;
    }
  }
}

export const autoSniperEngine = new AutoSniperEngine();
export default autoSniperEngine;
