import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import { PublicKey } from '@solana/web3.js';
import { ApiResponse, TokenDetectedEvent, Position, PriceAlert } from './types';
import { getSolBalance, getTokenBalances, isValidPublicKey } from './utils/wallet';
import { autoSniperEngine } from './engine/autoSniper';
import { userManager } from './utils/userManager';
import { logger, logError, systemLogger } from './utils/logger';
import config from './config';

const app = express();
const server = createServer(app);

// WebSocket Server for real-time communication
const wss = new WebSocketServer({ server });

// Store connected WebSocket clients
const connectedClients = new Set<any>();

// WebSocket connection handler
wss.on('connection', (ws, req) => {
  logger.info('🔌 Frontend client connected via WebSocket', {
    ip: req.socket.remoteAddress,
    userAgent: req.headers['user-agent'],
  });

  connectedClients.add(ws);

  // Send initial status to new client
  ws.send(JSON.stringify({
    type: 'status',
    data: autoSniperEngine.getStatus(),
    timestamp: new Date(),
  }));

  // Handle client messages
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      logger.info('📨 Received WebSocket message', { type: data.type });

      // Handle different message types
      switch (data.type) {
        case 'subscribe':
          // Client wants to subscribe to specific events
          ws.send(JSON.stringify({
            type: 'subscribed',
            data: { events: data.events || ['all'] },
            timestamp: new Date(),
          }));
          break;
        case 'ping':
          ws.send(JSON.stringify({ type: 'pong', timestamp: new Date() }));
          break;
      }
    } catch (error) {
      logger.warn('Invalid WebSocket message received', { error: error.message });
    }
  });

  // Handle client disconnect
  ws.on('close', () => {
    connectedClients.delete(ws);
    logger.info('🔌 Frontend client disconnected');
  });

  ws.on('error', (error) => {
    logger.error('WebSocket error', { error: error.message });
    connectedClients.delete(ws);
  });
});

// Function to broadcast to all connected clients
function broadcastToClients(data: any): void {
  const message = JSON.stringify(data);
  connectedClients.forEach((client) => {
    if (client.readyState === 1) { // WebSocket.OPEN
      try {
        client.send(message);
      } catch (error) {
        logger.warn('Failed to send WebSocket message to client', { error: error.message });
        connectedClients.delete(client);
      }
    } else {
      connectedClients.delete(client);
    }
  });
}

// Function to find an available port
async function findAvailablePort(startPort: number = 3000): Promise<number> {
  const net = await import('net');

  const isPortAvailable = (port: number): Promise<boolean> => {
    return new Promise((resolve) => {
      const server = net.createServer();

      server.listen(port, () => {
        server.close(() => {
          resolve(true);
        });
      });

      server.on('error', () => {
        resolve(false);
      });
    });
  };

  let currentPort = startPort;
  while (currentPort < startPort + 100) { // Try up to 100 ports
    if (await isPortAvailable(currentPort)) {
      return currentPort;
    }
    currentPort++;
  }

  throw new Error(`No available port found starting from ${startPort}`);
}

// Middleware
app.use(helmet());
app.use(cors({
  origin: config.api.corsOrigin,
  credentials: true,
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Integrated Sniper Bot Event Handlers for Real-time Updates
function setupSniperEventHandlers(): void {
  // Listen for token detections
  autoSniperEngine.on('tokenDetected', (event: TokenDetectedEvent) => {
    logger.info('🔍 Broadcasting token detection to frontend', {
      tokenAddress: event.tokenAddress,
      poolId: event.poolId,
      liquidity: event.liquidity,
    });

    broadcastToClients({
      type: 'tokenDetected',
      data: {
        tokenAddress: event.tokenAddress,
        poolId: event.poolId,
        liquidity: event.liquidity,
        timestamp: event.timestamp,
        metadata: event.metadata,
      },
      timestamp: new Date(),
    });
  });

  // Listen for buy executions
  autoSniperEngine.on('buyExecuted', (data: any) => {
    logger.info('💰 Broadcasting buy execution to frontend', {
      tokenAddress: data.tokenAddress,
      amount: data.amount,
      price: data.price,
    });

    broadcastToClients({
      type: 'buyExecuted',
      data: {
        ...data,
        timestamp: new Date(),
      },
      timestamp: new Date(),
    });
  });

  // Listen for sell executions
  autoSniperEngine.on('sellExecuted', (data: any) => {
    logger.info('💸 Broadcasting sell execution to frontend', {
      tokenAddress: data.tokenAddress,
      amount: data.amount,
      price: data.price,
      pnl: data.pnl,
    });

    broadcastToClients({
      type: 'sellExecuted',
      data: {
        ...data,
        timestamp: new Date(),
      },
      timestamp: new Date(),
    });
  });

  // Listen for price alerts
  autoSniperEngine.on('priceAlert', (alert: PriceAlert) => {
    logger.info('🚨 Broadcasting price alert to frontend', {
      tokenAddress: alert.tokenAddress,
      type: alert.type,
      currentPrice: alert.currentPrice,
      targetPrice: alert.targetPrice,
    });

    broadcastToClients({
      type: 'priceAlert',
      data: alert,
      timestamp: new Date(),
    });
  });

  // Listen for position updates
  autoSniperEngine.on('positionUpdate', (position: Position) => {
    broadcastToClients({
      type: 'positionUpdate',
      data: position,
      timestamp: new Date(),
    });
  });

  // Listen for status changes
  autoSniperEngine.on('statusChange', (status: any) => {
    broadcastToClients({
      type: 'statusChange',
      data: status,
      timestamp: new Date(),
    });
  });

  // Listen for errors
  autoSniperEngine.on('error', (error: any) => {
    logger.error('🚨 Broadcasting error to frontend', { error: error.message });

    broadcastToClients({
      type: 'error',
      data: {
        message: error.message,
        context: error.context || 'unknown',
        timestamp: new Date(),
      },
      timestamp: new Date(),
    });
  });

  logger.info('🔗 Sniper bot event handlers setup for real-time frontend updates');
}

// Request logging middleware
app.use((req, _res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.method === 'POST' ? req.body : undefined,
  });
  next();
});

// Health check endpoint
app.get('/health', (_req, res) => {
  res.json({
    success: true,
    message: '🎯 Sol Bullet Automated Sniper Bot is running',
    timestamp: new Date(),
    version: '1.0.0',
    sniper: autoSniperEngine.getStatus(),
  });
});

// Get wallet balance
app.get('/balance/:wallet', async (req, res) => {
  try {
    const { wallet } = req.params;

    if (!isValidPublicKey(wallet)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid wallet address',
      });
    }

    const publicKey = new PublicKey(wallet);
    const [solBalance, tokenBalances] = await Promise.all([
      getSolBalance(publicKey),
      getTokenBalances(publicKey),
    ]);

    const response: ApiResponse = {
      success: true,
      data: {
        wallet,
        balances: {
          sol: solBalance,
          tokens: tokenBalances,
        },
      },
      timestamp: new Date(),
    };

    return res.json(response);
  } catch (error) {
    logError(error as Error, { context: 'getBalance', wallet: req.params.wallet });
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch balance',
      message: (error as Error).message,
    });
  }
});

// Start automated sniper for user
app.post('/sniper/start', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required',
      });
    }

    await userManager.enableSniperForUser(userId);

    return res.json({
      success: true,
      message: '🎯 Automated sniper enabled for user',
      data: { userId },
      timestamp: new Date(),
    });

  } catch (error) {
    logError(error as Error, { context: 'startSniperEndpoint', body: req.body });
    return res.status(500).json({
      success: false,
      error: 'Failed to start sniper',
      message: (error as Error).message,
    });
  }
});

// Stop automated sniper for user
app.post('/sniper/stop', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required',
      });
    }

    await userManager.disableSniperForUser(userId);

    return res.json({
      success: true,
      message: '🛑 Automated sniper disabled for user',
      data: { userId },
      timestamp: new Date(),
    });

  } catch (error) {
    logError(error as Error, { context: 'stopSniperEndpoint', body: req.body });
    return res.status(500).json({
      success: false,
      error: 'Failed to stop sniper',
      message: (error as Error).message,
    });
  }
});

// Add wallet to user for sniper
app.post('/user/wallet', async (req, res) => {
  try {
    const { userId, privateKey } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required',
      });
    }

    const walletData = await userManager.addWalletToUser(userId, privateKey);

    return res.json({
      success: true,
      message: '💰 Wallet added to user for automated trading',
      data: {
        walletId: walletData.id,
        publicKey: walletData.publicKey,
      },
      timestamp: new Date(),
    });

  } catch (error) {
    logError(error as Error, { context: 'addWalletEndpoint', body: req.body });
    return res.status(500).json({
      success: false,
      error: 'Failed to add wallet',
      message: (error as Error).message,
    });
  }
});

// Helius webhook endpoint for automated detection
app.post('/webhook/helius', async (_req, res) => {
  try {
    // The autoSniperEngine handles this automatically
    res.json({
      success: true,
      message: 'Webhook received - automated processing active'
    });
  } catch (error) {
    logError(error as Error, { context: 'heliusWebhook' });
    res.status(500).json({ success: false, error: 'Webhook processing failed' });
  }
});

// Get automated sniper status
app.get('/status', (_req, res) => {
  const sniperStatus = autoSniperEngine.getStatus();

  res.json({
    success: true,
    message: '🎯 Automated Sniper Status',
    data: {
      ...sniperStatus,
      performance: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
      },
      jupiterIntegration: {
        apiUrl: config.jupiter.apiUrl,
        swapApiUrl: config.jupiter.swapApiUrl,
        enabled: true,
      },
    },
    timestamp: new Date(),
  });
});

// API version of status endpoint for frontend
app.get('/api/status', (_req, res) => {
  const sniperStatus = autoSniperEngine.getStatus();

  res.json({
    isRunning: sniperStatus.isRunning,
    poolsDetected: sniperStatus.poolsDetected || 0,
    recentTokens: sniperStatus.recentTokens || 0,
    config: {
      raydiumV4: sniperStatus.isRunning ? 'Connected' : 'Not connected',
      minLiquidity: config.trading.minLiquiditySol,
      buyAmount: config.trading.defaultBuyAmountSol,
      takeProfitPercent: config.profitLoss.takeProfitPercent,
      stopLossPercent: config.profitLoss.stopLossPercent,
      jupiterEnabled: true,
    },
    performance: {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
    },
  });
});

// Start sniper endpoint
app.post('/api/sniper/start', async (_req, res) => {
  try {
    if (autoSniperEngine.getStatus().isRunning) {
      return res.status(400).json({ error: 'Sniper is already running' });
    }

    await autoSniperEngine.start();
    return res.json({ success: true, message: 'Sniper started successfully' });
  } catch (error) {
    logError(error as Error, { context: 'startSniper' });
    return res.status(500).json({ error: 'Failed to start sniper' });
  }
});

// Stop sniper endpoint
app.post('/api/sniper/stop', async (_req, res) => {
  try {
    if (!autoSniperEngine.getStatus().isRunning) {
      return res.status(400).json({ error: 'Sniper is not running' });
    }

    await autoSniperEngine.stop();

    // Broadcast status change to connected clients
    broadcastToClients({
      type: 'statusChange',
      data: { isRunning: false, message: 'Sniper stopped' },
      timestamp: new Date(),
    });

    return res.json({ success: true, message: 'Sniper stopped successfully' });
  } catch (error) {
    logError(error as Error, { context: 'stopSniper' });
    return res.status(500).json({ error: 'Failed to stop sniper' });
  }
});

// Get active positions
app.get('/api/positions', async (_req, res) => {
  try {
    const positions = await autoSniperEngine.getActivePositions();
    res.json({
      success: true,
      data: positions,
      timestamp: new Date(),
    });
  } catch (error) {
    logError(error as Error, { context: 'getPositions' });
    res.status(500).json({ error: 'Failed to get positions' });
  }
});

// Get recent trades
app.get('/api/trades/recent', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 50;
    const trades = await autoSniperEngine.getRecentTrades(limit);
    res.json({
      success: true,
      data: trades,
      timestamp: new Date(),
    });
  } catch (error) {
    logError(error as Error, { context: 'getRecentTrades' });
    res.status(500).json({ error: 'Failed to get recent trades' });
  }
});

// Get price alerts
app.get('/api/alerts', async (_req, res) => {
  try {
    const alerts = await autoSniperEngine.getPriceAlerts();
    res.json({
      success: true,
      data: alerts,
      timestamp: new Date(),
    });
  } catch (error) {
    logError(error as Error, { context: 'getPriceAlerts' });
    res.status(500).json({ error: 'Failed to get price alerts' });
  }
});

// Get detected tokens
app.get('/api/tokens/detected', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 20;
    const tokens = await autoSniperEngine.getDetectedTokens(limit);
    res.json({
      success: true,
      data: tokens,
      timestamp: new Date(),
    });
  } catch (error) {
    logError(error as Error, { context: 'getDetectedTokens' });
    res.status(500).json({ error: 'Failed to get detected tokens' });
  }
});

// Manual buy endpoint with real-time updates
app.post('/api/buy', async (req, res) => {
  try {
    const { walletAddress, tokenAddress, amount, slippage } = req.body;

    if (!walletAddress || !tokenAddress) {
      return res.status(400).json({
        success: false,
        error: 'Wallet address and token address are required',
      });
    }

    // Broadcast buy attempt to clients
    broadcastToClients({
      type: 'buyAttempt',
      data: { walletAddress, tokenAddress, amount, slippage },
      timestamp: new Date(),
    });

    const result = await autoSniperEngine.executeBuy({
      walletAddress,
      tokenAddress,
      amount: amount || config.trading.defaultBuyAmountSol,
      slippage: slippage || config.trading.defaultSlippagePercent,
    });

    res.json({
      success: true,
      data: result,
      timestamp: new Date(),
    });

  } catch (error) {
    logError(error as Error, { context: 'manualBuy', body: req.body });
    res.status(500).json({
      success: false,
      error: 'Failed to execute buy',
      message: (error as Error).message,
    });
  }
});

// Manual sell endpoint with real-time updates
app.post('/api/sell', async (req, res) => {
  try {
    const { walletAddress, tokenAddress, percentage, sellAmountTokens } = req.body;

    if (!walletAddress || !tokenAddress) {
      return res.status(400).json({
        success: false,
        error: 'Wallet address and token address are required',
      });
    }

    // Broadcast sell attempt to clients
    broadcastToClients({
      type: 'sellAttempt',
      data: { walletAddress, tokenAddress, percentage, sellAmountTokens },
      timestamp: new Date(),
    });

    const result = await autoSniperEngine.executeSell({
      walletAddress,
      tokenAddress,
      percentage: percentage || 100,
      sellAmountTokens,
    });

    res.json({
      success: true,
      data: result,
      timestamp: new Date(),
    });

  } catch (error) {
    logError(error as Error, { context: 'manualSell', body: req.body });
    res.status(500).json({
      success: false,
      error: 'Failed to execute sell',
      message: (error as Error).message,
    });
  }
});

// WebSocket status endpoint
app.get('/api/websocket/status', (_req, res) => {
  res.json({
    success: true,
    data: {
      connectedClients: connectedClients.size,
      serverRunning: true,
    },
    timestamp: new Date(),
  });
});

// Error handling middleware
app.use((error: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  logError(error, {
    context: 'expressErrorHandler',
    method: req.method,
    path: req.path,
    body: req.body,
  });

  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: config.development.debugMode ? error.message : 'Something went wrong',
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    message: `${req.method} ${req.originalUrl} is not a valid endpoint`,
  });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  systemLogger.shutdown({ reason: 'SIGTERM' });
  await autoSniperEngine.stop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  systemLogger.shutdown({ reason: 'SIGINT' });
  await autoSniperEngine.stop();
  process.exit(0);
});

// Start server with automated sniper
async function startServer(): Promise<void> {
  try {
    logger.info('🎯 STARTING SOL BULLET INTEGRATED SNIPER BOT SERVER');

    // Setup sniper event handlers for real-time updates
    setupSniperEventHandlers();

    // Start the automated sniper engine
    await autoSniperEngine.start();

    // Find an available port starting from the configured port
    let availablePort: number;
    try {
      availablePort = await findAvailablePort(config.api.port);
    } catch (error) {
      logger.error('Failed to find available port', { error });
      availablePort = config.api.port; // Fallback to original port
    }

    // Start HTTP server with WebSocket support
    server.listen(availablePort, () => {
      systemLogger.startup({
        port: availablePort,
        nodeEnv: config.api.nodeEnv,
        pid: process.pid,
        sniperMode: 'AUTOMATED',
      });

      logger.info(`🎯 Sol Bullet AUTOMATED Sniper Bot running on port ${availablePort}`);
      if (availablePort !== config.api.port) {
        logger.info(`⚠️  Port ${config.api.port} was in use, using port ${availablePort} instead`);
      }
      logger.info(`🚀 AUTOMATED MODE: Bot will automatically detect and trade tokens`);
      logger.info(`📊 Jupiter Integration: ${config.jupiter.apiUrl}`);
      logger.info(`💰 Auto-Buy Amount: ${config.trading.defaultBuyAmountSol} SOL`);
      logger.info(`📈 Take Profit: ${config.profitLoss.takeProfitPercent}%`);
      logger.info(`📉 Stop Loss: ${config.profitLoss.stopLossPercent}%`);
      logger.info(`Environment: ${config.api.nodeEnv}`);
      logger.info(`Debug mode: ${config.development.debugMode}`);
      logger.info(`Dry run mode: ${config.development.dryRunMode}`);

      console.log('\n🎯 SOL BULLET AUTOMATED SNIPER BOT READY!');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      console.log('🔍 MONITORING: Raydium pools for new token launches');
      console.log('⚡ AUTO-BUY: Tokens that pass safety filters');
      console.log('📈 AUTO-SELL: When profit/loss targets are hit');
      console.log('🔄 JUPITER: All trades executed via Jupiter for best prices');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
    });

  } catch (error) {
    logError(error as Error, { context: 'startServer' });
    process.exit(1);
  }
}

// Start the server
startServer().catch((error) => {
  logError(error, { context: 'serverStartup' });
  process.exit(1);
});

export default app;
