{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../../../src/extensions/transferHook/state.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,uBAAuB,CAAC;AAE3E,OAAO,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAEtE,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAC5C,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,6BAA6B,CAAC;AAEnE,OAAO,EAAE,gCAAgC,EAAE,MAAM,iBAAiB,CAAC;AACnE,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AAUzC,iEAAiE;AACjE,MAAM,CAAC,MAAM,kBAAkB,GAAG,MAAM,CAAe,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AAEzG,MAAM,CAAC,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,IAAI,CAAC;AAE1D,MAAM,UAAU,eAAe,CAAC,IAAU;IACtC,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACjF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,kBAAkB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACpD,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC;AAWD,yEAAyE;AACzE,MAAM,CAAC,MAAM,yBAAyB,GAAG,MAAM,CAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAE7F,MAAM,CAAC,MAAM,0BAA0B,GAAG,yBAAyB,CAAC,IAAI,CAAC;AAEzE,MAAM,UAAU,sBAAsB,CAAC,OAAgB;IACnD,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC,mBAAmB,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAC3F,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,yBAAyB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAC3D,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC;AAED,MAAM,UAAU,0BAA0B,CAAC,IAAe,EAAE,SAAoB;IAC5E,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IACpE,OAAO,SAAS,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,CAAC;AAUD,2DAA2D;AAC3D,MAAM,CAAC,MAAM,sBAAsB,GAAG,MAAM,CAAmB;IAC3D,EAAE,CAAC,eAAe,CAAC;IACnB,IAAI,CAAC,EAAE,EAAE,eAAe,CAAC;IACzB,IAAI,CAAC,UAAU,CAAC;IAChB,IAAI,CAAC,YAAY,CAAC;CACrB,CAAC,CAAC;AAOH,2FAA2F;AAC3F,MAAM,CAAC,MAAM,0BAA0B,GAAG,MAAM,CAAuB;IACnE,GAAG,CAAC,OAAO,CAAC;IACZ,GAAG,CAAmB,sBAAsB,EAAE,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,eAAe,CAAC;CACtG,CAAC,CAAC;AASH,sEAAsE;AACtE,MAAM,CAAC,MAAM,iCAAiC,GAAG,MAAM,CAA8B;IACjF,GAAG,CAAC,0BAA0B,CAAC;IAC/B,GAAG,CAAC,QAAQ,CAAC;IACb,0BAA0B,CAAC,SAAS,CAAC,mBAAmB,CAAC;CAC5D,CAAC,CAAC;AAEH,gGAAgG;AAChG,MAAM,UAAU,oBAAoB,CAAC,OAA4B;IAC7D,MAAM,iBAAiB,GAAG,iCAAiC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC;IACnG,OAAO,iBAAiB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC7E,CAAC;AAED,6EAA6E;AAC7E,MAAM,CAAC,KAAK,UAAU,uBAAuB,CACzC,UAAsB,EACtB,SAA2B,EAC3B,aAA4B,EAC5B,eAAuB,EACvB,qBAAgC;IAEhC,IAAI,SAAS,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;QAChC,OAAO;YACH,MAAM,EAAE,IAAI,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC;YAC9C,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,UAAU,EAAE,SAAS,CAAC,UAAU;SACnC,CAAC;IACN,CAAC;IAED,IAAI,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC;IAElC,IAAI,SAAS,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;QAChC,SAAS,GAAG,qBAAqB,CAAC;IACtC,CAAC;SAAM,CAAC;QACJ,MAAM,YAAY,GAAG,SAAS,CAAC,aAAa,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QACxD,IAAI,aAAa,CAAC,MAAM,IAAI,YAAY,EAAE,CAAC;YACvC,MAAM,IAAI,gCAAgC,EAAE,CAAC;QACjD,CAAC;QACD,SAAS,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC;IACnD,CAAC;IAED,MAAM,KAAK,GAAG,MAAM,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;IACrG,MAAM,MAAM,GAAG,SAAS,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IAErE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,UAAU,EAAE,CAAC;AACtF,CAAC"}