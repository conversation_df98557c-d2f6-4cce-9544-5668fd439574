{"version": 3, "file": "user.d.ts", "sourceRoot": "", "sources": ["user.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC/C,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,SAAS,CAAC;AAEnD;;GAEG;AACH,MAAM,WAAW,OAAO;IACvB;;OAEG;IACH,EAAE,EAAE,SAAS,CAAC;IACd;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B;;;;OAIG;IACH,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB;;OAEG;IACH,GAAG,CAAC,EAAE,OAAO,CAAC;IACd;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACvB;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7B;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB;;;;OAIG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB;;;;OAIG;IACH,YAAY,CAAC,EAAE,eAAe,CAAC;IAC/B;;;;OAIG;IACH,YAAY,CAAC,EAAE,SAAS,CAAC;IACzB;;;;;OAKG;IACH,iBAAiB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAClC;;;;OAIG;IACH,sBAAsB,CAAC,EAAE,uBAAuB,GAAG,IAAI,CAAC;IACxD;;;;OAIG;IACH,YAAY,CAAC,EAAE,eAAe,GAAG,IAAI,CAAC;IACtC;;;;OAIG;IACH,aAAa,CAAC,EAAE,mBAAmB,GAAG,IAAI,CAAC;CAC3C;AAED;;GAEG;AACH,oBAAY,SAAS;IACpB;;OAEG;IACH,KAAK,IAAS;IACd;;OAEG;IACH,OAAO,IAAS;IAChB;;OAEG;IACH,SAAS,IAAS;IAClB;;OAEG;IACH,eAAe,IAAS;IACxB;;OAEG;IACH,MAAM,KAAS;IACf;;OAEG;IACH,qBAAqB,KAAS;IAC9B;;OAEG;IACH,qBAAqB,KAAS;IAC9B;;OAEG;IACH,qBAAqB,MAAS;IAC9B;;OAEG;IACH,qBAAqB,MAAS;IAC9B;;OAEG;IACH,qBAAqB,MAAS;IAC9B;;OAEG;IACH,cAAc,OAAU;IACxB;;OAEG;IACH,uBAAuB,OAAU;IACjC;;OAEG;IACH,eAAe,QAAU;IACzB;;OAEG;IACH,WAAW,QAAU;IACrB;;OAEG;IACH,iBAAiB,SAAU;IAC3B;;OAEG;IACH,kBAAkB,SAAU;IAC5B;;OAEG;IACH,mBAAmB,SAAU;IAC7B;;;;OAIG;IACH,OAAO,UAAU;IACjB;;OAEG;IACH,cAAc,UAAU;IACxB;;OAEG;IACH,eAAe,UAAU;IACzB;;;;;;;OAOG;IACH,WAAW,iBAAqB;IAChC;;;;;OAKG;IACH,YAAY,mBAAwB;IACpC;;;;;OAKG;IACH,sBAAsB,mBAAwB;CAC9C;AAED;;GAEG;AACH,oBAAY,eAAe;IAC1B,IAAI,IAAA;IACJ,YAAY,IAAA;IACZ,KAAK,IAAA;IACL,UAAU,IAAA;CACV;AAED;;GAEG;AACH,MAAM,WAAW,aAAa;IAC7B;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IACX;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;;;OAIG;IACH,IAAI,EAAE,iBAAiB,CAAC;IACxB;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;;;OAIG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC;IAC9C;;OAEG;IACH,QAAQ,EAAE,OAAO,CAAC;IAClB;;OAEG;IACH,WAAW,EAAE,OAAO,CAAC;IACrB;;OAEG;IACH,aAAa,EAAE,OAAO,CAAC;IACvB;;OAEG;IACH,YAAY,EAAE,OAAO,CAAC;IACtB;;;;OAIG;IACH,UAAU,EAAE,oBAAoB,CAAC;CACjC;AAED,oBAAY,iBAAiB;IAC5B,WAAW,iBAAiB;IAC5B,SAAS,cAAc;IACvB,OAAO,YAAY;IACnB,SAAS,WAAW;IACpB,WAAW,gBAAgB;IAC3B,MAAM,WAAW;IACjB,IAAI,SAAS;IACb,SAAS,cAAc;IACvB,QAAQ,aAAa;IACrB,MAAM,WAAW;IACjB,SAAS,cAAc;IACvB,eAAe,oBAAoB;IACnC,QAAQ,aAAa;IACrB,MAAM,WAAW;IACjB,kBAAkB,gBAAgB;IAClC,MAAM,WAAW;IACjB,SAAS,cAAc;IACvB,MAAM,WAAW;IACjB,OAAO,YAAY;IACnB,KAAK,UAAU;IACf,KAAK,UAAU;IACf,MAAM,WAAW;IACjB,MAAM,WAAW;IACjB,CAAC,YAAY;IACb;;OAEG;IACH,OAAO,YAAI;IACX,IAAI,SAAS;IACb,OAAO,YAAY;CACnB;AAED,oBAAY,oBAAoB;IAC/B;;OAEG;IACH,IAAI,IAAA;IACJ;;OAEG;IACH,QAAQ,IAAA;CACR;AAED;;GAEG;AACH,MAAM,WAAW,4BAA4B;IAC5C;;OAEG;IACH,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IAC7B;;OAEG;IACH,iBAAiB,EAAE,MAAM,GAAG,IAAI,CAAC;IACjC;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;CAC1C;AAED;;GAEG;AACH,MAAM,WAAW,uBAAuB;IACvC;;;;OAIG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,MAAM,EAAE,SAAS,CAAC;CAClB;AAED;;;;GAIG;AACH,MAAM,WAAW,eAAe;IAC/B;;OAEG;IACH,SAAS,CAAC,EAAE,gBAAgB,CAAC;CAC7B;AAED;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAChC;;OAEG;IACH,MAAM,EAAE,SAAS,CAAC;IAClB;;;;OAIG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,OAAO,EAAE,gBAAgB,CAAC;CAC1B;AAED;;GAEG;AACH,oBAAY,gBAAgB;IAC3B,KAAK,UAAU;IACf,SAAS,eAAe;IACxB,MAAM,WAAW;IACjB,MAAM,WAAW;IACjB,OAAO,YAAY;IACnB,MAAM,WAAW;IACjB,KAAK,UAAU;IACf,GAAG,QAAQ;IACX,IAAI,SAAS;IACb,MAAM,WAAW;IACjB,KAAK,UAAU;CACf;AAED;;GAEG;AACH,MAAM,WAAW,mBAAmB;IACnC;;OAEG;IACH,iBAAiB,EAAE,SAAS,GAAG,IAAI,CAAC;IACpC;;;OAGG;IACH,gBAAgB,EAAE,OAAO,GAAG,IAAI,CAAC;IACjC;;OAEG;IACH,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC;IACnB;;;;OAIG;IACH,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;CACrB"}