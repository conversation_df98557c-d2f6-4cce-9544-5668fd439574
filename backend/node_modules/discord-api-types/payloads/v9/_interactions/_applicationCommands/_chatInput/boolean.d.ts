import type { APIApplicationCommandOptionBase, APIInteractionDataOptionBase } from './base';
import type { ApplicationCommandOptionType } from './shared';
export type APIApplicationCommandBooleanOption = APIApplicationCommandOptionBase<ApplicationCommandOptionType.Boolean>;
export type APIApplicationCommandInteractionDataBooleanOption = APIInteractionDataOptionBase<ApplicationCommandOptionType.Boolean, boolean>;
//# sourceMappingURL=boolean.d.ts.map