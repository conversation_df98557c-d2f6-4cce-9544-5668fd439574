{"version": 3, "file": "gateway.d.ts", "sourceRoot": "", "sources": ["gateway.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,eAAe,CAAC;AAC/C,OAAO,KAAK,EAAE,gBAAgB,EAAE,eAAe,EAAE,MAAM,WAAW,CAAC;AACnE,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACxC,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,QAAQ,CAAC;AAEtC;;GAEG;AACH,MAAM,WAAW,cAAc;IAC9B;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;CACZ;AAED;;GAEG;AACH,MAAM,WAAW,iBAAkB,SAAQ,cAAc;IACxD;;;;OAIG;IACH,MAAM,EAAE,MAAM,CAAC;IACf;;;;OAIG;IACH,mBAAmB,EAAE,2BAA2B,CAAC;CACjD;AAED;;GAEG;AACH,MAAM,WAAW,2BAA2B;IAC3C;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,eAAe,EAAE,MAAM,CAAC;CACxB;AAED;;GAEG;AACH,MAAM,WAAW,gCAAgC;IAChD;;;;;;;OAOG;IACH,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC7C;;OAEG;IACH,MAAM,CAAC,EAAE,2BAA2B,CAAC;IACrC;;;;OAIG;IACH,UAAU,CAAC,EAAE,eAAe,EAAE,CAAC;IAC/B;;;;OAIG;IACH,aAAa,CAAC,EAAE,2BAA2B,CAAC;CAC5C;AAED;;GAEG;AACH,MAAM,WAAW,qBAAsB,SAAQ,gCAAgC;IAC9E;;OAEG;IACH,QAAQ,EAAE,SAAS,CAAC;CACpB;AAED;;GAEG;AACH,oBAAY,oBAAoB;IAC/B,MAAM,WAAW;IACjB,YAAY,QAAQ;IACpB,IAAI,SAAS;IACb;;OAEG;IACH,SAAS,cAAc;IACvB,OAAO,YAAY;CACnB;AAED,MAAM,MAAM,2BAA2B,GAAG,OAAO,CAAC,oBAAoB,EAAE,oBAAoB,CAAC,SAAS,CAAC,CAAC;AAExG;;GAEG;AACH,MAAM,WAAW,2BAA2B;IAC3C;;OAEG;IACH,OAAO,CAAC,EAAE,2BAA2B,CAAC;IACtC;;OAEG;IACH,MAAM,CAAC,EAAE,2BAA2B,CAAC;IACrC;;OAEG;IACH,GAAG,CAAC,EAAE,2BAA2B,CAAC;CAClC;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAC/B;;;;OAIG;IACH,EAAE,EAAE,MAAM,CAAC;IACX;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;;;OAIG;IACH,IAAI,EAAE,YAAY,CAAC;IACnB;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACpB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IACnB;;OAEG;IACH,UAAU,CAAC,EAAE,yBAAyB,CAAC;IACvC;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,cAAc,CAAC,EAAE,SAAS,CAAC;IAC3B;;;;OAIG;IACH,mBAAmB,CAAC,EAAE,iBAAiB,GAAG,IAAI,CAAC;IAC/C;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5B;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAC1B;;;;OAIG;IACH,KAAK,CAAC,EAAE,oBAAoB,CAAC;IAC7B;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB;;;;OAIG;IACH,KAAK,CAAC,EAAE,oBAAoB,CAAC;IAC7B;;;;OAIG;IACH,MAAM,CAAC,EAAE,qBAAqB,CAAC;IAC/B;;;;OAIG;IACH,OAAO,CAAC,EAAE,sBAAsB,CAAC;IACjC;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB;;;;;OAKG;IACH,KAAK,CAAC,EAAE,aAAa,CAAC;IACtB;;OAEG;IACH,OAAO,CAAC,EAAE,qBAAqB,EAAE,GAAG,MAAM,EAAE,CAAC;CAC7C;AAED;;;GAGG;AACH,oBAAY,gBAAgB;IAC3B,OAAO,YAAY;IACnB,IAAI,SAAS;IACb,OAAO,YAAY;IACnB,GAAG,QAAQ;IACX,OAAO,YAAY;IACnB,QAAQ,aAAa;IACrB,GAAG,QAAQ;IACX,GAAG,QAAQ;CACX;AAED;;GAEG;AACH,oBAAY,YAAY;IACvB;;OAEG;IACH,OAAO,IAAA;IACP;;OAEG;IACH,SAAS,IAAA;IACT;;OAEG;IACH,SAAS,IAAA;IACT;;OAEG;IACH,QAAQ,IAAA;IACR;;OAEG;IACH,MAAM,IAAA;IACN;;OAEG;IACH,SAAS,IAAA;CACT;AAED;;;;GAIG;AACH,oBAAY,iBAAiB;IAC5B;;OAEG;IACH,IAAI,IAAA;IACJ;;OAEG;IACH,KAAK,IAAA;IACL;;OAEG;IACH,OAAO,IAAA;CACP;AAED;;GAEG;AACH,MAAM,WAAW,yBAAyB;IACzC;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;CACb;AAED;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAEvG;;GAEG;AACH,MAAM,WAAW,oBAAoB;IACpC;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ;;OAEG;IACH,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;CAChD;AAED;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAAG,OAAO,CAC1C,MAAM,CAAC,aAAa,GAAG,YAAY,GAAG,WAAW,GAAG,aAAa,GAAG,YAAY,GAAG,WAAW,EAAE,MAAM,CAAC,CACvG,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,sBAAsB,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,OAAO,GAAG,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;AAE5F;;GAEG;AACH,oBAAY,aAAa;IACxB,QAAQ,IAAS;IACjB,IAAI,IAAS;IACb,QAAQ,IAAS;IACjB,WAAW,IAAS;IACpB,IAAI,KAAS;IACb,IAAI,KAAS;IACb,mBAAmB,KAAS;IAC5B,wBAAwB,MAAS;IACjC,QAAQ,MAAS;CACjB;AAED,MAAM,WAAW,qBAAqB;IACrC;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;CACZ;AAED;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACrC;;OAEG;IACH,QAAQ,EAAE,SAAS,CAAC;IACpB;;OAEG;IACH,WAAW,CAAC,EAAE,SAAS,EAAE,CAAC;IAC1B;;OAEG;IACH,OAAO,EAAE,gBAAgB,EAAE,CAAC;IAC5B;;OAEG;IACH,OAAO,EAAE,eAAe,EAAE,CAAC;CAC3B;AAED;;GAEG;AACH,MAAM,WAAW,0BAA0B;IAC1C;;OAEG;IACH,EAAE,EAAE,SAAS,CAAC;IACd;;OAEG;IACH,QAAQ,EAAE,SAAS,CAAC;IACpB;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;OAEG;IACH,aAAa,CAAC,EAAE,eAAe,EAAE,CAAC;IAClC;;OAEG;IACH,kBAAkB,CAAC,EAAE,SAAS,EAAE,CAAC;CACjC"}